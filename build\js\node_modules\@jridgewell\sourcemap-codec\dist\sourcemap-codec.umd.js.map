{"version": 3, "file": "sourcemap-codec.umd.js", "sources": ["../src/vlq.ts", "../src/strings.ts", "../src/scopes.ts", "../src/sourcemap-codec.ts"], "sourcesContent": ["import type { StringReader, StringWriter } from './strings';\n\nexport const comma = ','.charCodeAt(0);\nexport const semicolon = ';'.charCodeAt(0);\n\nconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\nconst intToChar = new Uint8Array(64); // 64 possible chars.\nconst charToInt = new Uint8Array(128); // z is 122 in ASCII\n\nfor (let i = 0; i < chars.length; i++) {\n  const c = chars.charCodeAt(i);\n  intToChar[i] = c;\n  charToInt[c] = i;\n}\n\nexport function decodeInteger(reader: StringReader, relative: number): number {\n  let value = 0;\n  let shift = 0;\n  let integer = 0;\n\n  do {\n    const c = reader.next();\n    integer = charToInt[c];\n    value |= (integer & 31) << shift;\n    shift += 5;\n  } while (integer & 32);\n\n  const shouldNegate = value & 1;\n  value >>>= 1;\n\n  if (shouldNegate) {\n    value = -0x80000000 | -value;\n  }\n\n  return relative + value;\n}\n\nexport function encodeInteger(builder: StringWriter, num: number, relative: number): number {\n  let delta = num - relative;\n\n  delta = delta < 0 ? (-delta << 1) | 1 : delta << 1;\n  do {\n    let clamped = delta & 0b011111;\n    delta >>>= 5;\n    if (delta > 0) clamped |= 0b100000;\n    builder.write(intToChar[clamped]);\n  } while (delta > 0);\n\n  return num;\n}\n\nexport function hasMoreVlq(reader: StringReader, max: number) {\n  if (reader.pos >= max) return false;\n  return reader.peek() !== comma;\n}\n", "const bufLength = 1024 * 16;\n\n// Provide a fallback for older environments.\nconst td =\n  typeof TextDecoder !== 'undefined'\n    ? /* #__PURE__ */ new TextDecoder()\n    : typeof Buffer !== 'undefined'\n    ? {\n        decode(buf: Uint8Array): string {\n          const out = Buffer.from(buf.buffer, buf.byteOffset, buf.byteLength);\n          return out.toString();\n        },\n      }\n    : {\n        decode(buf: Uint8Array): string {\n          let out = '';\n          for (let i = 0; i < buf.length; i++) {\n            out += String.fromCharCode(buf[i]);\n          }\n          return out;\n        },\n      };\n\nexport class StringWriter {\n  pos = 0;\n  private out = '';\n  private buffer = new Uint8Array(bufLength);\n\n  write(v: number): void {\n    const { buffer } = this;\n    buffer[this.pos++] = v;\n    if (this.pos === bufLength) {\n      this.out += td.decode(buffer);\n      this.pos = 0;\n    }\n  }\n\n  flush(): string {\n    const { buffer, out, pos } = this;\n    return pos > 0 ? out + td.decode(buffer.subarray(0, pos)) : out;\n  }\n}\n\nexport class StringReader {\n  pos = 0;\n  private declare buffer: string;\n\n  constructor(buffer: string) {\n    this.buffer = buffer;\n  }\n\n  next(): number {\n    return this.buffer.charCodeAt(this.pos++);\n  }\n\n  peek(): number {\n    return this.buffer.charCodeAt(this.pos);\n  }\n\n  indexOf(char: string): number {\n    const { buffer, pos } = this;\n    const idx = buffer.indexOf(char, pos);\n    return idx === -1 ? buffer.length : idx;\n  }\n}\n", "import { String<PERSON>ead<PERSON>, StringWriter } from './strings';\nimport { comma, decodeInteger, encodeInteger, hasMoreVlq, semicolon } from './vlq';\n\nconst EMPTY: any[] = [];\n\ntype Line = number;\ntype Column = number;\ntype Kind = number;\ntype Name = number;\ntype Var = number;\ntype SourcesIndex = number;\ntype ScopesIndex = number;\n\ntype Mix<A, B, O> = (A & O) | (B & O);\n\nexport type OriginalScope = Mix<\n  [Line, Column, Line, Column, Kind],\n  [Line, Column, Line, Column, Kind, Name],\n  { vars: Var[] }\n>;\n\nexport type GeneratedRange = Mix<\n  [Line, Column, Line, Column],\n  [Line, Column, Line, Column, SourcesIndex, ScopesIndex],\n  {\n    callsite: CallSite | null;\n    bindings: Binding[];\n    isScope: boolean;\n  }\n>;\nexport type CallSite = [SourcesIndex, Line, Column];\ntype Binding = BindingExpressionRange[];\nexport type BindingExpressionRange = [Name] | [Name, Line, Column];\n\nexport function decodeOriginalScopes(input: string): OriginalScope[] {\n  const { length } = input;\n  const reader = new StringReader(input);\n  const scopes: OriginalScope[] = [];\n  const stack: OriginalScope[] = [];\n  let line = 0;\n\n  for (; reader.pos < length; reader.pos++) {\n    line = decodeInteger(reader, line);\n    const column = decodeInteger(reader, 0);\n\n    if (!hasMoreVlq(reader, length)) {\n      const last = stack.pop()!;\n      last[2] = line;\n      last[3] = column;\n      continue;\n    }\n\n    const kind = decodeInteger(reader, 0);\n    const fields = decodeInteger(reader, 0);\n    const hasName = fields & 0b0001;\n\n    const scope: OriginalScope = (\n      hasName ? [line, column, 0, 0, kind, decodeInteger(reader, 0)] : [line, column, 0, 0, kind]\n    ) as OriginalScope;\n\n    let vars: Var[] = EMPTY;\n    if (hasMoreVlq(reader, length)) {\n      vars = [];\n      do {\n        const varsIndex = decodeInteger(reader, 0);\n        vars.push(varsIndex);\n      } while (hasMoreVlq(reader, length));\n    }\n    scope.vars = vars;\n\n    scopes.push(scope);\n    stack.push(scope);\n  }\n\n  return scopes;\n}\n\nexport function encodeOriginalScopes(scopes: OriginalScope[]): string {\n  const writer = new StringWriter();\n\n  for (let i = 0; i < scopes.length; ) {\n    i = _encodeOriginalScopes(scopes, i, writer, [0]);\n  }\n\n  return writer.flush();\n}\n\nfunction _encodeOriginalScopes(\n  scopes: OriginalScope[],\n  index: number,\n  writer: StringWriter,\n  state: [\n    number, // GenColumn\n  ],\n): number {\n  const scope = scopes[index];\n  const { 0: startLine, 1: startColumn, 2: endLine, 3: endColumn, 4: kind, vars } = scope;\n\n  if (index > 0) writer.write(comma);\n\n  state[0] = encodeInteger(writer, startLine, state[0]);\n  encodeInteger(writer, startColumn, 0);\n  encodeInteger(writer, kind, 0);\n\n  const fields = scope.length === 6 ? 0b0001 : 0;\n  encodeInteger(writer, fields, 0);\n  if (scope.length === 6) encodeInteger(writer, scope[5], 0);\n\n  for (const v of vars) {\n    encodeInteger(writer, v, 0);\n  }\n\n  for (index++; index < scopes.length; ) {\n    const next = scopes[index];\n    const { 0: l, 1: c } = next;\n    if (l > endLine || (l === endLine && c >= endColumn)) {\n      break;\n    }\n    index = _encodeOriginalScopes(scopes, index, writer, state);\n  }\n\n  writer.write(comma);\n  state[0] = encodeInteger(writer, endLine, state[0]);\n  encodeInteger(writer, endColumn, 0);\n\n  return index;\n}\n\nexport function decodeGeneratedRanges(input: string): GeneratedRange[] {\n  const { length } = input;\n  const reader = new StringReader(input);\n  const ranges: GeneratedRange[] = [];\n  const stack: GeneratedRange[] = [];\n\n  let genLine = 0;\n  let definitionSourcesIndex = 0;\n  let definitionScopeIndex = 0;\n  let callsiteSourcesIndex = 0;\n  let callsiteLine = 0;\n  let callsiteColumn = 0;\n  let bindingLine = 0;\n  let bindingColumn = 0;\n\n  do {\n    const semi = reader.indexOf(';');\n    let genColumn = 0;\n\n    for (; reader.pos < semi; reader.pos++) {\n      genColumn = decodeInteger(reader, genColumn);\n\n      if (!hasMoreVlq(reader, semi)) {\n        const last = stack.pop()!;\n        last[2] = genLine;\n        last[3] = genColumn;\n        continue;\n      }\n\n      const fields = decodeInteger(reader, 0);\n      const hasDefinition = fields & 0b0001;\n      const hasCallsite = fields & 0b0010;\n      const hasScope = fields & 0b0100;\n\n      let callsite: CallSite | null = null;\n      let bindings: Binding[] = EMPTY;\n      let range: GeneratedRange;\n      if (hasDefinition) {\n        const defSourcesIndex = decodeInteger(reader, definitionSourcesIndex);\n        definitionScopeIndex = decodeInteger(\n          reader,\n          definitionSourcesIndex === defSourcesIndex ? definitionScopeIndex : 0,\n        );\n\n        definitionSourcesIndex = defSourcesIndex;\n        range = [genLine, genColumn, 0, 0, defSourcesIndex, definitionScopeIndex] as GeneratedRange;\n      } else {\n        range = [genLine, genColumn, 0, 0] as GeneratedRange;\n      }\n\n      range.isScope = !!hasScope;\n\n      if (hasCallsite) {\n        const prevCsi = callsiteSourcesIndex;\n        const prevLine = callsiteLine;\n        callsiteSourcesIndex = decodeInteger(reader, callsiteSourcesIndex);\n        const sameSource = prevCsi === callsiteSourcesIndex;\n        callsiteLine = decodeInteger(reader, sameSource ? callsiteLine : 0);\n        callsiteColumn = decodeInteger(\n          reader,\n          sameSource && prevLine === callsiteLine ? callsiteColumn : 0,\n        );\n\n        callsite = [callsiteSourcesIndex, callsiteLine, callsiteColumn];\n      }\n      range.callsite = callsite;\n\n      if (hasMoreVlq(reader, semi)) {\n        bindings = [];\n        do {\n          bindingLine = genLine;\n          bindingColumn = genColumn;\n          const expressionsCount = decodeInteger(reader, 0);\n          let expressionRanges: BindingExpressionRange[];\n          if (expressionsCount < -1) {\n            expressionRanges = [[decodeInteger(reader, 0)]];\n            for (let i = -1; i > expressionsCount; i--) {\n              const prevBl = bindingLine;\n              bindingLine = decodeInteger(reader, bindingLine);\n              bindingColumn = decodeInteger(reader, bindingLine === prevBl ? bindingColumn : 0);\n              const expression = decodeInteger(reader, 0);\n              expressionRanges.push([expression, bindingLine, bindingColumn]);\n            }\n          } else {\n            expressionRanges = [[expressionsCount]];\n          }\n          bindings.push(expressionRanges);\n        } while (hasMoreVlq(reader, semi));\n      }\n      range.bindings = bindings;\n\n      ranges.push(range);\n      stack.push(range);\n    }\n\n    genLine++;\n    reader.pos = semi + 1;\n  } while (reader.pos < length);\n\n  return ranges;\n}\n\nexport function encodeGeneratedRanges(ranges: GeneratedRange[]): string {\n  if (ranges.length === 0) return '';\n\n  const writer = new StringWriter();\n\n  for (let i = 0; i < ranges.length; ) {\n    i = _encodeGeneratedRanges(ranges, i, writer, [0, 0, 0, 0, 0, 0, 0]);\n  }\n\n  return writer.flush();\n}\n\nfunction _encodeGeneratedRanges(\n  ranges: GeneratedRange[],\n  index: number,\n  writer: StringWriter,\n  state: [\n    number, // GenLine\n    number, // GenColumn\n    number, // DefSourcesIndex\n    number, // DefScopesIndex\n    number, // CallSourcesIndex\n    number, // CallLine\n    number, // CallColumn\n  ],\n): number {\n  const range = ranges[index];\n  const {\n    0: startLine,\n    1: startColumn,\n    2: endLine,\n    3: endColumn,\n    isScope,\n    callsite,\n    bindings,\n  } = range;\n\n  if (state[0] < startLine) {\n    catchupLine(writer, state[0], startLine);\n    state[0] = startLine;\n    state[1] = 0;\n  } else if (index > 0) {\n    writer.write(comma);\n  }\n\n  state[1] = encodeInteger(writer, range[1], state[1]);\n\n  const fields =\n    (range.length === 6 ? 0b0001 : 0) | (callsite ? 0b0010 : 0) | (isScope ? 0b0100 : 0);\n  encodeInteger(writer, fields, 0);\n\n  if (range.length === 6) {\n    const { 4: sourcesIndex, 5: scopesIndex } = range;\n    if (sourcesIndex !== state[2]) {\n      state[3] = 0;\n    }\n    state[2] = encodeInteger(writer, sourcesIndex, state[2]);\n    state[3] = encodeInteger(writer, scopesIndex, state[3]);\n  }\n\n  if (callsite) {\n    const { 0: sourcesIndex, 1: callLine, 2: callColumn } = range.callsite!;\n    if (sourcesIndex !== state[4]) {\n      state[5] = 0;\n      state[6] = 0;\n    } else if (callLine !== state[5]) {\n      state[6] = 0;\n    }\n    state[4] = encodeInteger(writer, sourcesIndex, state[4]);\n    state[5] = encodeInteger(writer, callLine, state[5]);\n    state[6] = encodeInteger(writer, callColumn, state[6]);\n  }\n\n  if (bindings) {\n    for (const binding of bindings) {\n      if (binding.length > 1) encodeInteger(writer, -binding.length, 0);\n      const expression = binding[0][0];\n      encodeInteger(writer, expression, 0);\n      let bindingStartLine = startLine;\n      let bindingStartColumn = startColumn;\n      for (let i = 1; i < binding.length; i++) {\n        const expRange = binding[i];\n        bindingStartLine = encodeInteger(writer, expRange[1]!, bindingStartLine);\n        bindingStartColumn = encodeInteger(writer, expRange[2]!, bindingStartColumn);\n        encodeInteger(writer, expRange[0]!, 0);\n      }\n    }\n  }\n\n  for (index++; index < ranges.length; ) {\n    const next = ranges[index];\n    const { 0: l, 1: c } = next;\n    if (l > endLine || (l === endLine && c >= endColumn)) {\n      break;\n    }\n    index = _encodeGeneratedRanges(ranges, index, writer, state);\n  }\n\n  if (state[0] < endLine) {\n    catchupLine(writer, state[0], endLine);\n    state[0] = endLine;\n    state[1] = 0;\n  } else {\n    writer.write(comma);\n  }\n  state[1] = encodeInteger(writer, endColumn, state[1]);\n\n  return index;\n}\n\nfunction catchupLine(writer: StringWriter, lastLine: number, line: number) {\n  do {\n    writer.write(semicolon);\n  } while (++lastLine < line);\n}\n", "import { comma, decodeInteger, encodeInteger, hasMoreVlq, semicolon } from './vlq';\nimport { StringWriter, StringReader } from './strings';\n\nexport {\n  decodeOriginalScopes,\n  encodeOriginalScopes,\n  decodeGeneratedRanges,\n  encodeGeneratedRanges,\n} from './scopes';\nexport type { OriginalScope, GeneratedRange, CallSite, BindingExpressionRange } from './scopes';\n\nexport type SourceMapSegment =\n  | [number]\n  | [number, number, number, number]\n  | [number, number, number, number, number];\nexport type SourceMapLine = SourceMapSegment[];\nexport type SourceMapMappings = SourceMapLine[];\n\nexport function decode(mappings: string): SourceMapMappings {\n  const { length } = mappings;\n  const reader = new StringReader(mappings);\n  const decoded: SourceMapMappings = [];\n  let genColumn = 0;\n  let sourcesIndex = 0;\n  let sourceLine = 0;\n  let sourceColumn = 0;\n  let namesIndex = 0;\n\n  do {\n    const semi = reader.indexOf(';');\n    const line: SourceMapLine = [];\n    let sorted = true;\n    let lastCol = 0;\n    genColumn = 0;\n\n    while (reader.pos < semi) {\n      let seg: SourceMapSegment;\n\n      genColumn = decodeInteger(reader, genColumn);\n      if (genColumn < lastCol) sorted = false;\n      lastCol = genColumn;\n\n      if (hasMoreVlq(reader, semi)) {\n        sourcesIndex = decodeInteger(reader, sourcesIndex);\n        sourceLine = decodeInteger(reader, sourceLine);\n        sourceColumn = decodeInteger(reader, sourceColumn);\n\n        if (hasMoreVlq(reader, semi)) {\n          namesIndex = decodeInteger(reader, namesIndex);\n          seg = [genColumn, sourcesIndex, sourceLine, sourceColumn, namesIndex];\n        } else {\n          seg = [genColumn, sourcesIndex, sourceLine, sourceColumn];\n        }\n      } else {\n        seg = [genColumn];\n      }\n\n      line.push(seg);\n      reader.pos++;\n    }\n\n    if (!sorted) sort(line);\n    decoded.push(line);\n    reader.pos = semi + 1;\n  } while (reader.pos <= length);\n\n  return decoded;\n}\n\nfunction sort(line: SourceMapSegment[]) {\n  line.sort(sortComparator);\n}\n\nfunction sortComparator(a: SourceMapSegment, b: SourceMapSegment): number {\n  return a[0] - b[0];\n}\n\nexport function encode(decoded: SourceMapMappings): string;\nexport function encode(decoded: Readonly<SourceMapMappings>): string;\nexport function encode(decoded: Readonly<SourceMapMappings>): string {\n  const writer = new StringWriter();\n  let sourcesIndex = 0;\n  let sourceLine = 0;\n  let sourceColumn = 0;\n  let namesIndex = 0;\n\n  for (let i = 0; i < decoded.length; i++) {\n    const line = decoded[i];\n    if (i > 0) writer.write(semicolon);\n    if (line.length === 0) continue;\n\n    let genColumn = 0;\n\n    for (let j = 0; j < line.length; j++) {\n      const segment = line[j];\n      if (j > 0) writer.write(comma);\n\n      genColumn = encodeInteger(writer, segment[0], genColumn);\n\n      if (segment.length === 1) continue;\n      sourcesIndex = encodeInteger(writer, segment[1], sourcesIndex);\n      sourceLine = encodeInteger(writer, segment[2], sourceLine);\n      sourceColumn = encodeInteger(writer, segment[3], sourceColumn);\n\n      if (segment.length === 4) continue;\n      namesIndex = encodeInteger(writer, segment[4], namesIndex);\n    }\n  }\n\n  return writer.flush();\n}\n"], "names": [], "mappings": ";;;;;;IAEO,MAAM,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IAChC,MAAM,SAAS,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IAE3C,MAAM,KAAK,GAAG,kEAAkE,CAAC;IACjF,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;IACrC,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;IAEtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,MAAM,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAC9B,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACjB,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;KAClB;aAEe,aAAa,CAAC,MAAoB,EAAE,QAAgB;QAClE,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,OAAO,GAAG,CAAC,CAAC;QAEhB,GAAG;YACD,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;YACxB,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YACvB,KAAK,IAAI,CAAC,OAAO,GAAG,EAAE,KAAK,KAAK,CAAC;YACjC,KAAK,IAAI,CAAC,CAAC;SACZ,QAAQ,OAAO,GAAG,EAAE,EAAE;QAEvB,MAAM,YAAY,GAAG,KAAK,GAAG,CAAC,CAAC;QAC/B,KAAK,MAAM,CAAC,CAAC;QAEb,IAAI,YAAY,EAAE;YAChB,KAAK,GAAG,CAAC,UAAU,GAAG,CAAC,KAAK,CAAC;SAC9B;QAED,OAAO,QAAQ,GAAG,KAAK,CAAC;IAC1B,CAAC;aAEe,aAAa,CAAC,OAAqB,EAAE,GAAW,EAAE,QAAgB;QAChF,IAAI,KAAK,GAAG,GAAG,GAAG,QAAQ,CAAC;QAE3B,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,CAAC;QACnD,GAAG;YACD,IAAI,OAAO,GAAG,KAAK,GAAG,QAAQ,CAAC;YAC/B,KAAK,MAAM,CAAC,CAAC;YACb,IAAI,KAAK,GAAG,CAAC;gBAAE,OAAO,IAAI,QAAQ,CAAC;YACnC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;SACnC,QAAQ,KAAK,GAAG,CAAC,EAAE;QAEpB,OAAO,GAAG,CAAC;IACb,CAAC;aAEe,UAAU,CAAC,MAAoB,EAAE,GAAW;QAC1D,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG;YAAE,OAAO,KAAK,CAAC;QACpC,OAAO,MAAM,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC;IACjC;;ICtDA,MAAM,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;IAE5B;IACA,MAAM,EAAE,GACN,OAAO,WAAW,KAAK,WAAW;0BACd,IAAI,WAAW,EAAE;UACjC,OAAO,MAAM,KAAK,WAAW;cAC7B;gBACE,MAAM,CAAC,GAAe;oBACpB,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;oBACpE,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;iBACvB;aACF;cACD;gBACE,MAAM,CAAC,GAAe;oBACpB,IAAI,GAAG,GAAG,EAAE,CAAC;oBACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;wBACnC,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;qBACpC;oBACD,OAAO,GAAG,CAAC;iBACZ;aACF,CAAC;UAEK,YAAY;QAAzB;YACE,QAAG,GAAG,CAAC,CAAC;YACA,QAAG,GAAG,EAAE,CAAC;YACT,WAAM,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,CAAC;SAe5C;QAbC,KAAK,CAAC,CAAS;YACb,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;YACxB,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;YACvB,IAAI,IAAI,CAAC,GAAG,KAAK,SAAS,EAAE;gBAC1B,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;gBAC9B,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;aACd;SACF;QAED,KAAK;YACH,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;YAClC,OAAO,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;SACjE;KACF;UAEY,YAAY;QAIvB,YAAY,MAAc;YAH1B,QAAG,GAAG,CAAC,CAAC;YAIN,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;SACtB;QAED,IAAI;YACF,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;SAC3C;QAED,IAAI;YACF,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SACzC;QAED,OAAO,CAAC,IAAY;YAClB,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;YAC7B,MAAM,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YACtC,OAAO,GAAG,KAAK,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC;SACzC;;;IC5DH,MAAM,KAAK,GAAU,EAAE,CAAC;aA+BR,oBAAoB,CAAC,KAAa;QAChD,MAAM,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;QACzB,MAAM,MAAM,GAAG,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC;QACvC,MAAM,MAAM,GAAoB,EAAE,CAAC;QACnC,MAAM,KAAK,GAAoB,EAAE,CAAC;QAClC,IAAI,IAAI,GAAG,CAAC,CAAC;QAEb,OAAO,MAAM,CAAC,GAAG,GAAG,MAAM,EAAE,MAAM,CAAC,GAAG,EAAE,EAAE;YACxC,IAAI,GAAG,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YACnC,MAAM,MAAM,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YAExC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE;gBAC/B,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,EAAG,CAAC;gBAC1B,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;gBACf,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;gBACjB,SAAS;aACV;YAED,MAAM,IAAI,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YACtC,MAAM,MAAM,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YACxC,MAAM,OAAO,GAAG,MAAM,GAAG,MAAM,CAAC;YAEhC,MAAM,KAAK,IACT,OAAO,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAC3E,CAAC;YAEnB,IAAI,IAAI,GAAU,KAAK,CAAC;YACxB,IAAI,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE;gBAC9B,IAAI,GAAG,EAAE,CAAC;gBACV,GAAG;oBACD,MAAM,SAAS,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;oBAC3C,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;iBACtB,QAAQ,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE;aACtC;YACD,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;YAElB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnB,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACnB;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;aAEe,oBAAoB,CAAC,MAAuB;QAC1D,MAAM,MAAM,GAAG,IAAI,YAAY,EAAE,CAAC;QAElC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,GAAI;YACnC,CAAC,GAAG,qBAAqB,CAAC,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;SACnD;QAED,OAAO,MAAM,CAAC,KAAK,EAAE,CAAC;IACxB,CAAC;IAED,SAAS,qBAAqB,CAC5B,MAAuB,EACvB,KAAa,EACb,MAAoB,EACpB,KAEC;QAED,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5B,MAAM,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC;QAExF,IAAI,KAAK,GAAG,CAAC;YAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAEnC,KAAK,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD,aAAa,CAAC,MAAM,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC;QACtC,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAE/B,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC;QAC/C,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;QACjC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;YAAE,aAAa,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAE3D,KAAK,MAAM,CAAC,IAAI,IAAI,EAAE;YACpB,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SAC7B;QAED,KAAK,KAAK,EAAE,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,GAAI;YACrC,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;YAC3B,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;YAC5B,IAAI,CAAC,GAAG,OAAO,KAAK,CAAC,KAAK,OAAO,IAAI,CAAC,IAAI,SAAS,CAAC,EAAE;gBACpD,MAAM;aACP;YACD,KAAK,GAAG,qBAAqB,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;SAC7D;QAED,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACpB,KAAK,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD,aAAa,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;QAEpC,OAAO,KAAK,CAAC;IACf,CAAC;aAEe,qBAAqB,CAAC,KAAa;QACjD,MAAM,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;QACzB,MAAM,MAAM,GAAG,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC;QACvC,MAAM,MAAM,GAAqB,EAAE,CAAC;QACpC,MAAM,KAAK,GAAqB,EAAE,CAAC;QAEnC,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,IAAI,sBAAsB,GAAG,CAAC,CAAC;QAC/B,IAAI,oBAAoB,GAAG,CAAC,CAAC;QAC7B,IAAI,oBAAoB,GAAG,CAAC,CAAC;QAC7B,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,IAAI,aAAa,GAAG,CAAC,CAAC;QAEtB,GAAG;YACD,MAAM,IAAI,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YACjC,IAAI,SAAS,GAAG,CAAC,CAAC;YAElB,OAAO,MAAM,CAAC,GAAG,GAAG,IAAI,EAAE,MAAM,CAAC,GAAG,EAAE,EAAE;gBACtC,SAAS,GAAG,aAAa,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;gBAE7C,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;oBAC7B,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,EAAG,CAAC;oBAC1B,IAAI,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;oBAClB,IAAI,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;oBACpB,SAAS;iBACV;gBAED,MAAM,MAAM,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;gBACxC,MAAM,aAAa,GAAG,MAAM,GAAG,MAAM,CAAC;gBACtC,MAAM,WAAW,GAAG,MAAM,GAAG,MAAM,CAAC;gBACpC,MAAM,QAAQ,GAAG,MAAM,GAAG,MAAM,CAAC;gBAEjC,IAAI,QAAQ,GAAoB,IAAI,CAAC;gBACrC,IAAI,QAAQ,GAAc,KAAK,CAAC;gBAChC,IAAI,KAAqB,CAAC;gBAC1B,IAAI,aAAa,EAAE;oBACjB,MAAM,eAAe,GAAG,aAAa,CAAC,MAAM,EAAE,sBAAsB,CAAC,CAAC;oBACtE,oBAAoB,GAAG,aAAa,CAClC,MAAM,EACN,sBAAsB,KAAK,eAAe,GAAG,oBAAoB,GAAG,CAAC,CACtE,CAAC;oBAEF,sBAAsB,GAAG,eAAe,CAAC;oBACzC,KAAK,GAAG,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,eAAe,EAAE,oBAAoB,CAAmB,CAAC;iBAC7F;qBAAM;oBACL,KAAK,GAAG,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,CAAmB,CAAC;iBACtD;gBAED,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,QAAQ,CAAC;gBAE3B,IAAI,WAAW,EAAE;oBACf,MAAM,OAAO,GAAG,oBAAoB,CAAC;oBACrC,MAAM,QAAQ,GAAG,YAAY,CAAC;oBAC9B,oBAAoB,GAAG,aAAa,CAAC,MAAM,EAAE,oBAAoB,CAAC,CAAC;oBACnE,MAAM,UAAU,GAAG,OAAO,KAAK,oBAAoB,CAAC;oBACpD,YAAY,GAAG,aAAa,CAAC,MAAM,EAAE,UAAU,GAAG,YAAY,GAAG,CAAC,CAAC,CAAC;oBACpE,cAAc,GAAG,aAAa,CAC5B,MAAM,EACN,UAAU,IAAI,QAAQ,KAAK,YAAY,GAAG,cAAc,GAAG,CAAC,CAC7D,CAAC;oBAEF,QAAQ,GAAG,CAAC,oBAAoB,EAAE,YAAY,EAAE,cAAc,CAAC,CAAC;iBACjE;gBACD,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;gBAE1B,IAAI,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;oBAC5B,QAAQ,GAAG,EAAE,CAAC;oBACd,GAAG;wBACD,WAAW,GAAG,OAAO,CAAC;wBACtB,aAAa,GAAG,SAAS,CAAC;wBAC1B,MAAM,gBAAgB,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;wBAClD,IAAI,gBAA0C,CAAC;wBAC/C,IAAI,gBAAgB,GAAG,CAAC,CAAC,EAAE;4BACzB,gBAAgB,GAAG,CAAC,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;4BAChD,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC,EAAE,EAAE;gCAC1C,MAAM,MAAM,GAAG,WAAW,CAAC;gCAC3B,WAAW,GAAG,aAAa,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;gCACjD,aAAa,GAAG,aAAa,CAAC,MAAM,EAAE,WAAW,KAAK,MAAM,GAAG,aAAa,GAAG,CAAC,CAAC,CAAC;gCAClF,MAAM,UAAU,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;gCAC5C,gBAAgB,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC,CAAC;6BACjE;yBACF;6BAAM;4BACL,gBAAgB,GAAG,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC;yBACzC;wBACD,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;qBACjC,QAAQ,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;iBACpC;gBACD,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;gBAE1B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACnB,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACnB;YAED,OAAO,EAAE,CAAC;YACV,MAAM,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC;SACvB,QAAQ,MAAM,CAAC,GAAG,GAAG,MAAM,EAAE;QAE9B,OAAO,MAAM,CAAC;IAChB,CAAC;aAEe,qBAAqB,CAAC,MAAwB;QAC5D,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,EAAE,CAAC;QAEnC,MAAM,MAAM,GAAG,IAAI,YAAY,EAAE,CAAC;QAElC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,GAAI;YACnC,CAAC,GAAG,sBAAsB,CAAC,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;SACtE;QAED,OAAO,MAAM,CAAC,KAAK,EAAE,CAAC;IACxB,CAAC;IAED,SAAS,sBAAsB,CAC7B,MAAwB,EACxB,KAAa,EACb,MAAoB,EACpB,KAQC;QAED,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5B,MAAM,EACJ,CAAC,EAAE,SAAS,EACZ,CAAC,EAAE,WAAW,EACd,CAAC,EAAE,OAAO,EACV,CAAC,EAAE,SAAS,EACZ,OAAO,EACP,QAAQ,EACR,QAAQ,GACT,GAAG,KAAK,CAAC;QAEV,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,SAAS,EAAE;YACxB,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;YACzC,KAAK,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;YACrB,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;SACd;aAAM,IAAI,KAAK,GAAG,CAAC,EAAE;YACpB,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;SACrB;QAED,KAAK,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAErD,MAAM,MAAM,GACV,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG,MAAM,GAAG,CAAC,KAAK,QAAQ,GAAG,MAAM,GAAG,CAAC,CAAC,IAAI,OAAO,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;QACvF,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;QAEjC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB,MAAM,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,WAAW,EAAE,GAAG,KAAK,CAAC;YAClD,IAAI,YAAY,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE;gBAC7B,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;aACd;YACD,KAAK,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACzD,KAAK,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SACzD;QAED,IAAI,QAAQ,EAAE;YACZ,MAAM,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,UAAU,EAAE,GAAG,KAAK,CAAC,QAAS,CAAC;YACxE,IAAI,YAAY,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE;gBAC7B,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;gBACb,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;aACd;iBAAM,IAAI,QAAQ,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE;gBAChC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;aACd;YACD,KAAK,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACzD,KAAK,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACrD,KAAK,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SACxD;QAED,IAAI,QAAQ,EAAE;YACZ,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;gBAC9B,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC;oBAAE,aAAa,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;gBAClE,MAAM,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjC,aAAa,CAAC,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC;gBACrC,IAAI,gBAAgB,GAAG,SAAS,CAAC;gBACjC,IAAI,kBAAkB,GAAG,WAAW,CAAC;gBACrC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACvC,MAAM,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;oBAC5B,gBAAgB,GAAG,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAE,EAAE,gBAAgB,CAAC,CAAC;oBACzE,kBAAkB,GAAG,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAE,EAAE,kBAAkB,CAAC,CAAC;oBAC7E,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAE,EAAE,CAAC,CAAC,CAAC;iBACxC;aACF;SACF;QAED,KAAK,KAAK,EAAE,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,GAAI;YACrC,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;YAC3B,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;YAC5B,IAAI,CAAC,GAAG,OAAO,KAAK,CAAC,KAAK,OAAO,IAAI,CAAC,IAAI,SAAS,CAAC,EAAE;gBACpD,MAAM;aACP;YACD,KAAK,GAAG,sBAAsB,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;SAC9D;QAED,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,EAAE;YACtB,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;YACvC,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;YACnB,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;SACd;aAAM;YACL,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;SACrB;QACD,KAAK,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAEtD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,SAAS,WAAW,CAAC,MAAoB,EAAE,QAAgB,EAAE,IAAY;QACvE,GAAG;YACD,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;SACzB,QAAQ,EAAE,QAAQ,GAAG,IAAI,EAAE;IAC9B;;aCtUgB,MAAM,CAAC,QAAgB;QACrC,MAAM,EAAE,MAAM,EAAE,GAAG,QAAQ,CAAC;QAC5B,MAAM,MAAM,GAAG,IAAI,YAAY,CAAC,QAAQ,CAAC,CAAC;QAC1C,MAAM,OAAO,GAAsB,EAAE,CAAC;QACtC,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,GAAG;YACD,MAAM,IAAI,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YACjC,MAAM,IAAI,GAAkB,EAAE,CAAC;YAC/B,IAAI,MAAM,GAAG,IAAI,CAAC;YAClB,IAAI,OAAO,GAAG,CAAC,CAAC;YAChB,SAAS,GAAG,CAAC,CAAC;YAEd,OAAO,MAAM,CAAC,GAAG,GAAG,IAAI,EAAE;gBACxB,IAAI,GAAqB,CAAC;gBAE1B,SAAS,GAAG,aAAa,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;gBAC7C,IAAI,SAAS,GAAG,OAAO;oBAAE,MAAM,GAAG,KAAK,CAAC;gBACxC,OAAO,GAAG,SAAS,CAAC;gBAEpB,IAAI,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;oBAC5B,YAAY,GAAG,aAAa,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;oBACnD,UAAU,GAAG,aAAa,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;oBAC/C,YAAY,GAAG,aAAa,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;oBAEnD,IAAI,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;wBAC5B,UAAU,GAAG,aAAa,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;wBAC/C,GAAG,GAAG,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;qBACvE;yBAAM;wBACL,GAAG,GAAG,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC;qBAC3D;iBACF;qBAAM;oBACL,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC;iBACnB;gBAED,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACf,MAAM,CAAC,GAAG,EAAE,CAAC;aACd;YAED,IAAI,CAAC,MAAM;gBAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YACxB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnB,MAAM,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC;SACvB,QAAQ,MAAM,CAAC,GAAG,IAAI,MAAM,EAAE;QAE/B,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,SAAS,IAAI,CAAC,IAAwB;QACpC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAC5B,CAAC;IAED,SAAS,cAAc,CAAC,CAAmB,EAAE,CAAmB;QAC9D,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;aAIe,MAAM,CAAC,OAAoC;QACzD,MAAM,MAAM,GAAG,IAAI,YAAY,EAAE,CAAC;QAClC,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACvC,MAAM,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YACxB,IAAI,CAAC,GAAG,CAAC;gBAAE,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YACnC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;gBAAE,SAAS;YAEhC,IAAI,SAAS,GAAG,CAAC,CAAC;YAElB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACpC,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBACxB,IAAI,CAAC,GAAG,CAAC;oBAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAE/B,SAAS,GAAG,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;gBAEzD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;oBAAE,SAAS;gBACnC,YAAY,GAAG,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;gBAC/D,UAAU,GAAG,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;gBAC3D,YAAY,GAAG,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;gBAE/D,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;oBAAE,SAAS;gBACnC,UAAU,GAAG,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;aAC5D;SACF;QAED,OAAO,MAAM,CAAC,KAAK,EAAE,CAAC;IACxB;;;;;;;;;;;;;;;"}