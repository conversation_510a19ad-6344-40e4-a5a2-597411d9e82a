plugins {
    kotlin("multiplatform") version "2.0.20"
}

group = "top.yunp"
version = "1.0-SNAPSHOT"

repositories {
    mavenCentral()
}

kotlin {
    sourceSets {
        jsMain {
            dependencies {
                implementation(project(":threejs"))
                implementation(npm("three", "0.168.0"))
            }
        }
    }

    js {
        browser {
        }
        binaries.executable()
    }
}