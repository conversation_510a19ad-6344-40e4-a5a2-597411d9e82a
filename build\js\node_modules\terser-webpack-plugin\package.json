{"name": "terser-webpack-plugin", "version": "5.3.14", "description": "Terser plugin for webpack", "license": "MIT", "repository": "webpack-contrib/terser-webpack-plugin", "author": "webpack Contrib Team", "homepage": "https://github.com/webpack-contrib/terser-webpack-plugin", "bugs": "https://github.com/webpack-contrib/terser-webpack-plugin/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/index.js", "types": "types/index.d.ts", "engines": {"node": ">= 10.13.0"}, "scripts": {"clean": "del-cli dist types", "prebuild": "npm run clean", "build:types": "tsc --declaration --emitDeclarationOnly --outDir types && prettier \"types/**/*.ts\" --write", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "build": "npm-run-all -p \"build:**\"", "commitlint": "commitlint --from=master", "security": "npm audit --production", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint:spelling": "cspell \"**/*.*\"", "lint:types": "tsc --pretty --noEmit", "lint": "npm-run-all -l -p \"lint:**\"", "fix:js": "npm run lint:js -- --fix", "fix:prettier": "npm run lint:prettier -- --write", "fix": "npm-run-all -l fix:js fix:prettier", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "husky install && npm run build", "release": "standard-version"}, "files": ["dist", "types"], "peerDependencies": {"webpack": "^5.1.0"}, "peerDependenciesMeta": {"@swc/core": {"optional": true}, "uglify-js": {"optional": true}, "esbuild": {"optional": true}}, "dependencies": {"@jridgewell/trace-mapping": "^0.3.25", "jest-worker": "^27.4.5", "schema-utils": "^4.3.0", "serialize-javascript": "^6.0.2", "terser": "^5.31.1"}, "devDependencies": {"@babel/cli": "^7.24.7", "@babel/core": "^7.24.7", "@babel/preset-env": "^7.24.7", "@commitlint/cli": "^17.7.1", "@commitlint/config-conventional": "^17.7.0", "@swc/core": "^1.3.102", "@types/node": "^18.15.11", "@types/serialize-javascript": "^5.0.2", "@types/uglify-js": "^3.17.5", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^28.1.2", "copy-webpack-plugin": "^9.0.1", "cross-env": "^7.0.3", "cspell": "^6.31.2", "del": "^6.0.0", "del-cli": "^3.0.1", "esbuild": "^0.19.11", "eslint": "^7.32.0", "eslint-config-prettier": "^8.9.0", "eslint-plugin-import": "^2.28.1", "file-loader": "^6.2.0", "husky": "^7.0.2", "jest": "^27.5.1", "lint-staged": "^13.2.3", "memfs": "^3.4.13", "npm-run-all": "^4.1.5", "prettier": "^2.8.7", "standard-version": "^9.3.1", "typescript": "^4.9.5", "uglify-js": "^3.18.0", "webpack": "^5.92.1", "webpack-cli": "^4.10.0", "worker-loader": "^3.0.8"}, "keywords": ["uglify", "uglify-js", "uglify-es", "terser", "webpack", "webpack-plugin", "minification", "compress", "compressor", "min", "minification", "minifier", "minify", "optimize", "optimizer"]}