body {
	margin: 0;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
	font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
	font-size: 14px;
	line-height: 1.6;
	overscroll-behavior: none;
	overflow: hidden;
}

a {
	color: #ff0;
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

button {
	cursor: pointer;
	text-transform: uppercase;
	transition: all 0.3s ease;
	border-radius: 8px;
	font-weight: 600;
	letter-spacing: 1px;
}

#info {
	position: absolute;
	top: 0px;
	width: 100%;
	padding: 10px;
	box-sizing: border-box;
	text-align: center;
	-moz-user-select: none;
	-webkit-user-select: none;
	-ms-user-select: none;
	user-select: none;
	pointer-events: none;
	z-index: 1; /* TODO Solve this in HTML */
}

a, button, input, select {
	pointer-events: auto;
}

.lil-gui {
	z-index: 2 !important; /* TODO Solve this in HTML */
}

@media all and ( max-width: 640px ) {
	.lil-gui.root { 
		right: auto;
		top: auto;
		max-height: 50%;
		max-width: 80%;
		bottom: 0;
		left: 0;
	}
}

#overlay {
	position: absolute;
	font-size: 18px;
	z-index: 2;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
	background: rgba(0,0,0,0.8);
	backdrop-filter: blur(10px);
}

	#overlay button {
		background: linear-gradient(45deg, #ff6b6b, #ee5a24);
		border: none;
		border-radius: 50px;
		color: #ffffff;
		padding: 16px 32px;
		text-transform: uppercase;
		cursor: pointer;
		font-size: 16px;
		font-weight: bold;
		box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
		transform: translateY(0);
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	}

	#overlay button:hover {
		transform: translateY(-3px);
		box-shadow: 0 12px 35px rgba(255, 107, 107, 0.4);
		background: linear-gradient(45deg, #ff5252, #d63031);
	}

	#overlay button:active {
		transform: translateY(-1px);
		box-shadow: 0 6px 20px rgba(255, 107, 107, 0.3);
	}

#notSupported {
	width: 50%;
	margin: auto;
	background: linear-gradient(45deg, #ff6b6b, #ee5a24);
	margin-top: 20px;
	padding: 20px;
	border-radius: 12px;
	box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
	text-align: center;
	font-weight: 600;
}

/* 添加加载动画 */
@keyframes pulse {
	0% { opacity: 0.6; }
	50% { opacity: 1; }
	100% { opacity: 0.6; }
}

.loading {
	animation: pulse 2s infinite;
}

/* 添加淡入动画 */
@keyframes fadeIn {
	from { opacity: 0; transform: translateY(20px); }
	to { opacity: 1; transform: translateY(0); }
}

#overlay {
	animation: fadeIn 0.5s ease-out;
}

/* 响应式设计改进 */
@media (max-width: 768px) {
	#overlay button {
		padding: 14px 28px;
		font-size: 14px;
	}

	body {
		font-size: 12px;
	}
}
