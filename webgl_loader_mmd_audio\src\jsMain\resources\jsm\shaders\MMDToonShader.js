/**
 * MMD Toon Shader
 *
 * This shader is extended from MeshPhongMaterial, and merged algorithms with
 * MeshToonMaterial and MeshMetcapMaterial.
 * Ideas came from https://github.com/mrdoob/three.js/issues/19609
 *
 * Combining steps:
 *  * Declare matcap uniform.
 *  * Add gradientmap_pars_fragment.
 *  * Use gradient irradiances instead of dotNL irradiance from MeshPhongMaterial.
 *    (Replace lights_phong_pars_fragment with lights_mmd_toon_pars_fragment)
 *  * Add mmd_toon_matcap_fragment.
 */

import { UniformsUtils, ShaderLib } from 'three';

const lights_mmd_toon_pars_fragment = /* glsl */`
varying vec3 vViewPosition;

struct BlinnPhongMaterial {

	vec3 diffuseColor;
	vec3 specularColor;
	float specularShininess;
	float specularStrength;

};

// 增强的卡通光照计算
vec3 getEnhancedToonIrradiance( const in vec3 normal, const in vec3 lightDirection ) {
	float dotNL = dot( normal, lightDirection );

	// 更强的卡通化分层效果
	float toonFactor = smoothstep( 0.0, 0.1, dotNL ) * 0.4 +
	                   smoothstep( 0.1, 0.3, dotNL ) * 0.3 +
	                   smoothstep( 0.3, 0.7, dotNL ) * 0.3;

	// 增加环境光贡献，减少过暗区域
	toonFactor = max( toonFactor, 0.3 );

	return vec3( toonFactor );
}

void RE_Direct_BlinnPhong( const in IncidentLight directLight, const in vec3 geometryPosition, const in vec3 geometryNormal, const in vec3 geometryViewDir, const in vec3 geometryClearcoatNormal, const in BlinnPhongMaterial material, inout ReflectedLight reflectedLight ) {

	// 使用增强的卡通光照
	vec3 irradiance = getEnhancedToonIrradiance( geometryNormal, directLight.direction ) * directLight.color;

	reflectedLight.directDiffuse += irradiance * BRDF_Lambert( material.diffuseColor );

	// 减少高光强度，让效果更柔和
	reflectedLight.directSpecular += irradiance * BRDF_BlinnPhong( directLight.direction, geometryViewDir, geometryNormal, material.specularColor, material.specularShininess ) * material.specularStrength * 0.6;

}

void RE_IndirectDiffuse_BlinnPhong( const in vec3 irradiance, const in vec3 geometryPosition, const in vec3 geometryNormal, const in vec3 geometryViewDir, const in vec3 geometryClearcoatNormal, const in BlinnPhongMaterial material, inout ReflectedLight reflectedLight ) {

	// 增强间接光照，让模型更明亮
	reflectedLight.indirectDiffuse += irradiance * BRDF_Lambert( material.diffuseColor ) * 1.2;

}

#define RE_Direct				RE_Direct_BlinnPhong
#define RE_IndirectDiffuse		RE_IndirectDiffuse_BlinnPhong
`;

const mmd_toon_matcap_fragment = /* glsl */`
#ifdef USE_MATCAP

	vec3 viewDir = normalize( vViewPosition );
	vec3 x = normalize( vec3( viewDir.z, 0.0, - viewDir.x ) );
	vec3 y = cross( viewDir, x );
	vec2 uv = vec2( dot( x, normal ), dot( y, normal ) ) * 0.495 + 0.5;
	vec4 matcapColor = texture2D( matcap, uv );

	#ifdef MATCAP_BLENDING_MULTIPLY

		outgoingLight *= matcapColor.rgb;

	#elif defined( MATCAP_BLENDING_ADD )

		// 增强 matcap 效果，提升卡通质感
		outgoingLight += matcapColor.rgb * 0.8;

	#endif

#endif

// 增加色彩饱和度和对比度，增强卡通效果
outgoingLight = mix( outgoingLight, outgoingLight * outgoingLight, 0.2 );
outgoingLight = mix( vec3( dot( outgoingLight, vec3( 0.299, 0.587, 0.114 ) ) ), outgoingLight, 1.3 );
`;

const MMDToonShader = {

	name: 'MMDToonShader',

	defines: {
		TOON: true,
		MATCAP: true,
		MATCAP_BLENDING_ADD: true,
		USE_GRADIENTMAP: true,
	},

	uniforms: UniformsUtils.merge( [
		ShaderLib.toon.uniforms,
		ShaderLib.phong.uniforms,
		ShaderLib.matcap.uniforms,
	] ),

	vertexShader:
		ShaderLib.phong.vertexShader
			.replace(
				'#include <envmap_pars_vertex>',
				''
			)
			.replace(
				'#include <envmap_vertex>',
				''
			),

	fragmentShader:
		ShaderLib.phong.fragmentShader
			.replace(
				'#include <common>',
				`
					#ifdef USE_MATCAP
						uniform sampler2D matcap;
					#endif

					#include <common>
				`
			)
			.replace(
				'#include <envmap_common_pars_fragment>',
				`
					#include <gradientmap_pars_fragment>
				`
			)
			.replace(
				'#include <envmap_pars_fragment>',
				''
			)
			.replace(
				'#include <lights_phong_pars_fragment>',
				lights_mmd_toon_pars_fragment
			)
			.replace(
				'#include <envmap_fragment>',
				`
					${mmd_toon_matcap_fragment}
				`
			)

};

export { MMDToonShader };
