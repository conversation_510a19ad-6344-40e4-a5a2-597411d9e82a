(function (factory) {
  if (typeof define === 'function' && define.amd)
    define(['exports'], factory);
  else if (typeof exports === 'object')
    factory(module.exports);
  else
    globalThis['top.yunp:threejs'] = factory(typeof globalThis['top.yunp:threejs'] === 'undefined' ? {} : globalThis['top.yunp:threejs']);
}(function (_) {
  'use strict';
  //region block: pre-declaration
  //endregion
  return _;
}));

//# sourceMappingURL=mmd-threejs.js.map
