<!DOCTYPE html>
<html lang="en">
<head>
    <title>three.js webgl - loaders - MMD loader</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0">
    <link type="text/css" rel="stylesheet" href="main.css">
    <style>
        body {
            color: #444;
            background: #fff;
        }

        a {
            color: #08f;
        }
    </style>
</head>

<body>
<div id="overlay">
    <button id="startButton">Play</button>
</div>
<div id="info">
    <a href="https://threejs.org" target="_blank" rel="noopener">three.js</a> - MMDLoader test<br/>
    <a href="https://github.com/mrdoob/three.js/tree/master/examples/models/mmd#readme" target="_blank" rel="noopener">MMD
        Assets license</a><br/>
    Copyright
    <a href="https://sites.google.com/view/evpvp/" target="_blank" rel="noopener">Model Data</a>
    <a href="http://www.nicovideo.jp/watch/sm13147122" target="_blank" rel="noopener">Dance Data</a>
    <a href="http://www.nicovideo.jp/watch/sm11938255" target="_blank" rel="noopener">Audio Data</a><br/>
    Camera is customized from <a href="http://www.nicovideo.jp/watch/sm19168559" target="_blank" rel="noopener">this
    Data</a><br>
    <a href="https://yunp.top/sc1/v/802"
       target="_blank">
        本示例 Kotlin 源码</a>
</div>

<script src="jsm/libs/ammo.wasm.js"></script>
<script src="webgl_loader_mmd_audio.js"></script>
</body>
</html>
