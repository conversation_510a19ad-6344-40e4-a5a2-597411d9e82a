(function (factory) {
  if (typeof define === 'function' && define.amd)
    define(['exports', 'three', './kotlin-kotlin-stdlib.js'], factory);
  else if (typeof exports === 'object')
    factory(module.exports, require('three'), require('./kotlin-kotlin-stdlib.js'));
  else {
    if (typeof three === 'undefined') {
      throw new Error("Error loading module 'top.yunp:webgl_loader_mmd_audio'. Its dependency 'three' was not found. Please, check whether 'three' is loaded prior to 'top.yunp:webgl_loader_mmd_audio'.");
    }
    if (typeof globalThis['kotlin-kotlin-stdlib'] === 'undefined') {
      throw new Error("Error loading module 'top.yunp:webgl_loader_mmd_audio'. Its dependency 'kotlin-kotlin-stdlib' was not found. Please, check whether 'kotlin-kotlin-stdlib' is loaded prior to 'top.yunp:webgl_loader_mmd_audio'.");
    }
    globalThis['top.yunp:webgl_loader_mmd_audio'] = factory(typeof globalThis['top.yunp:webgl_loader_mmd_audio'] === 'undefined' ? {} : globalThis['top.yunp:webgl_loader_mmd_audio'], three, globalThis['kotlin-kotlin-stdlib']);
  }
}(function (_, $module$three, kotlin_kotlin) {
  'use strict';
  //region block: imports
  var Clock = $module$three.Clock;
  var PerspectiveCamera = $module$three.PerspectiveCamera;
  var Scene = $module$three.Scene;
  var Color = $module$three.Color;
  var AudioListener = $module$three.AudioListener;
  var AmbientLight = $module$three.AmbientLight;
  var DirectionalLight = $module$three.DirectionalLight;
  var WebGLRenderer = $module$three.WebGLRenderer;
  var Audio = $module$three.Audio;
  var AudioLoader = $module$three.AudioLoader;
  var Unit_instance = kotlin_kotlin.$_$.a;
  var to = kotlin_kotlin.$_$.c;
  var json = kotlin_kotlin.$_$.b;
  //endregion
  //region block: pre-declaration
  //endregion
  function get_OutlineEffect() {
    _init_properties_Main_kt__xi25uv();
    return OutlineEffect;
  }
  var OutlineEffect;
  function get_MMDLoader() {
    _init_properties_Main_kt__xi25uv();
    return MMDLoader;
  }
  var MMDLoader;
  function get_MMDAnimationHelper() {
    _init_properties_Main_kt__xi25uv();
    return MMDAnimationHelper;
  }
  var MMDAnimationHelper;
  function get_jsNew() {
    _init_properties_Main_kt__xi25uv();
    return jsNew;
  }
  var jsNew;
  function get_jsNew1() {
    _init_properties_Main_kt__xi25uv();
    return jsNew1;
  }
  var jsNew1;
  function onProgress(xhr) {
    _init_properties_Main_kt__xi25uv();
    if (xhr.lengthComputable) {
      var percentComplete = xhr.loaded / xhr.total * 100;
      console.log(Math.round(percentComplete, 2) + '% downloaded');
    }
  }
  function init() {
    _init_properties_Main_kt__xi25uv();
    var overlay = document.getElementById('overlay');
    if (overlay == null)
      null;
    else {
      overlay.remove();
    }
    var container = document.createElement('div');
    var tmp1_safe_receiver = document.body;
    if (tmp1_safe_receiver == null)
      null;
    else
      tmp1_safe_receiver.appendChild(container);
    var mesh = {_v: null};
    var ready = {_v: false};
    var clock = new Clock();
    var camera = new PerspectiveCamera(35.0, window.innerWidth / window.innerHeight, 1.0, 2000.0);
    camera.position.set(0.0, 10.0, 30.0);
    var scene = new Scene();
    scene.background = new Color(8900331);
    var listener = new AudioListener();
    camera.add(listener);
    scene.add(camera);
    var ambient = new AmbientLight(16777215, 1.0);
    scene.add(ambient);
    var directionalLight = new DirectionalLight(16777215, 1.5);
    directionalLight.position.set(-1.0, 1.0, 1.0).normalize();
    // Inline function 'kotlin.js.asDynamic' call
    directionalLight.castShadow = true;
    // Inline function 'kotlin.js.asDynamic' call
    directionalLight.shadow.mapSize.width = 2048;
    // Inline function 'kotlin.js.asDynamic' call
    directionalLight.shadow.mapSize.height = 2048;
    scene.add(directionalLight);
    var fillLight = new DirectionalLight(16775388, 0.8);
    fillLight.position.set(1.0, 0.5, -1.0).normalize();
    scene.add(fillLight);
    var topLight = new DirectionalLight(15135743, 0.5);
    topLight.position.set(0.0, 1.0, 0.0);
    scene.add(topLight);
    var renderer = new WebGLRenderer(json([to('antialias', true), to('alpha', true), to('powerPreference', 'high-performance')]));
    renderer.setPixelRatio(window.devicePixelRatio);
    renderer.setSize(window.innerWidth, window.innerHeight);
    // Inline function 'kotlin.js.asDynamic' call
    renderer.shadowMap.enabled = true;
    // Inline function 'kotlin.js.asDynamic' call
    renderer.shadowMap.type = 2;
    // Inline function 'kotlin.js.asDynamic' call
    renderer.toneMapping = 1;
    // Inline function 'kotlin.js.asDynamic' call
    renderer.toneMappingExposure = 1.2;
    // Inline function 'kotlin.js.asDynamic' call
    renderer.outputColorSpace = 'srgb';
    container.appendChild(renderer.domElement);
    var effect = get_jsNew1()(get_OutlineEffect(), renderer);
    var modelFile = 'models/mmd/miku/miku_v2.pmd';
    // Inline function 'kotlin.arrayOf' call
    // Inline function 'kotlin.js.unsafeCast' call
    // Inline function 'kotlin.js.asDynamic' call
    var vmdFiles = ['models/mmd/vmds/wavefile_v2.vmd'];
    // Inline function 'kotlin.arrayOf' call
    // Inline function 'kotlin.js.unsafeCast' call
    // Inline function 'kotlin.js.asDynamic' call
    var cameraFiles = ['models/mmd/vmds/wavefile_camera.vmd'];
    var audioFile = 'models/mmd/audios/wavefile_short.mp3';
    var audioParams = json([to('delayTime', 160.0 * 1 / 30)]);
    var helper = get_jsNew()(get_MMDAnimationHelper());
    var loader = get_jsNew()(get_MMDLoader());
    var tmp = init$lambda(mesh, helper, loader, cameraFiles, camera, audioFile, listener, audioParams, scene, ready);
    loader.loadWithAnimation(modelFile, vmdFiles, tmp, init$lambda_0);
    var tmp_0 = window;
    tmp_0.addEventListener('resize', init$lambda_1(camera, effect));
    renderer.setAnimationLoop(init$lambda_2(ready, helper, clock, effect, scene, camera));
  }
  function main() {
    _init_properties_Main_kt__xi25uv();
    var tmp0_safe_receiver = document.querySelector('#startButton');
    if (tmp0_safe_receiver == null)
      null;
    else {
      tmp0_safe_receiver.addEventListener('click', main$lambda);
    }
    var tmp1_safe_receiver = document.body;
    if (tmp1_safe_receiver == null)
      null;
    else {
      tmp1_safe_receiver.addEventListener('dblclick', main$lambda_0);
    }
  }
  function init$lambda$lambda$lambda($listener, $helper, $audioParams, $scene, $mesh, $ready) {
    return function (buffer) {
      var audio = (new Audio($listener)).setBuffer(buffer);
      $helper.add(audio, $audioParams);
      $scene.add($mesh._v);
      $ready._v = true;
      return Unit_instance;
    };
  }
  function init$lambda$lambda$lambda_0(it) {
    _init_properties_Main_kt__xi25uv();
    onProgress(it);
    return Unit_instance;
  }
  function init$lambda$lambda($helper, $camera, $audioFile, $listener, $audioParams, $scene, $mesh, $ready) {
    return function (cameraAnimation) {
      $helper.add($camera, json([to('animation', cameraAnimation)]));
      var tmp = new AudioLoader();
      var tmp_0 = init$lambda$lambda$lambda($listener, $helper, $audioParams, $scene, $mesh, $ready);
      tmp.load($audioFile, tmp_0, init$lambda$lambda$lambda_0);
      return Unit_instance;
    };
  }
  function init$lambda$lambda_0(it) {
    _init_properties_Main_kt__xi25uv();
    onProgress(it);
    return Unit_instance;
  }
  function init$lambda($mesh, $helper, $loader, $cameraFiles, $camera, $audioFile, $listener, $audioParams, $scene, $ready) {
    return function (mmd) {
      $mesh._v = mmd.mesh;
      $helper.add($mesh._v, json([to('animation', mmd.animation), to('physics', true)]));
      var tmp = init$lambda$lambda($helper, $camera, $audioFile, $listener, $audioParams, $scene, $mesh, $ready);
      return $loader.loadAnimation($cameraFiles, $camera, tmp, init$lambda$lambda_0);
    };
  }
  function init$lambda_0(it) {
    _init_properties_Main_kt__xi25uv();
    onProgress(it);
    return Unit_instance;
  }
  function init$lambda_1($camera, $effect) {
    return function (_unused_var__etf5q3) {
      $camera.aspect = window.innerWidth / window.innerHeight;
      $camera.updateProjectionMatrix();
      $effect.setSize(window.innerWidth, window.innerHeight);
      return Unit_instance;
    };
  }
  function init$lambda_2($ready, $helper, $clock, $effect, $scene, $camera) {
    return function () {
      var tmp;
      if ($ready._v) {
        $helper.update($clock.getDelta());
        tmp = Unit_instance;
      }
      $effect.render($scene, $camera);
      return Unit_instance;
    };
  }
  function main$lambda(it) {
    _init_properties_Main_kt__xi25uv();
    var tmp = Ammo();
    tmp.then(main$lambda$lambda);
    return Unit_instance;
  }
  function main$lambda$lambda(it) {
    _init_properties_Main_kt__xi25uv();
    init();
    return Unit_instance;
  }
  function main$lambda_0(it) {
    _init_properties_Main_kt__xi25uv();
    var tmp0_safe_receiver = document.body;
    if (tmp0_safe_receiver == null)
      null;
    else
      tmp0_safe_receiver.requestFullscreen();
    return Unit_instance;
  }
  var properties_initialized_Main_kt_gqj46d;
  function _init_properties_Main_kt__xi25uv() {
    if (!properties_initialized_Main_kt_gqj46d) {
      properties_initialized_Main_kt_gqj46d = true;
      OutlineEffect = require('./jsm/effects/OutlineEffect.js').OutlineEffect;
      MMDLoader = require('./jsm/loaders/MMDLoader.js').MMDLoader;
      MMDAnimationHelper = require('./jsm/animation/MMDAnimationHelper.js').MMDAnimationHelper;
      jsNew = function (Clazz) {
        return new Clazz();
      };
      jsNew1 = function (Clazz, arg) {
        return new Clazz(arg);
      };
    }
  }
  function mainWrapper() {
    main();
  }
  mainWrapper();
  return _;
}));

//# sourceMappingURL=mmd-webgl_loader_mmd_audio.js.map
