{"version": 3, "sources": ["../../../../../webgl_loader_mmd_audio/src/jsMain/kotlin/Main.kt", "../../../../../webgl_loader_mmd_audio/build/compileSync/js/main/productionExecutable/kotlin/js/src/kotlin/dynamic.kt", "../../../../../webgl_loader_mmd_audio/build/compileSync/js/main/productionExecutable/kotlin/js/builtins/Library.kt"], "sourcesContent": [null, null, null], "names": ["<get-OutlineEffect>", "<get-M<PERSON><PERSON><PERSON>der>", "<get-MMDAnimationHelper>", "<get-jsNew>", "<get-jsNew1>", "onProgress", "xhr", "percentComplete", "init", "overlay", "container", "mesh", "ready", "clock", "camera", "scene", "listener", "ambient", "directionalLight", "fillLight", "topLight", "renderer", "effect", "modelFile", "vmdFiles", "cameraFiles", "audioFile", "audioParams", "helper", "loader", "main", "init$lambda$lambda$lambda", "$listener", "$helper", "$audioParams", "$scene", "$mesh", "$ready", "init$lambda$lambda$lambda$lambda", "buffer", "audio", "it", "init$lambda$lambda", "$camera", "$audioFile", "cameraAnimation", "init$lambda", "$loader", "$cameraFiles", "mmd", "$effect", "$clock", "main$lambda", "main$lambda$lambda", "<init properties Main.kt>", "Clazz", "arg"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAQgCA,CAAAA,EAAA;A;IAAA,oB;EAEyC,C;;wBAAEC,CAAAA,EAAA;A;IAAA,gB;EACf,C;;iCAAEC,CAAAA,EAAA;A;IAAA,yB;EAC0B,C;;oBAAIC,CAAAA,EAAA;A;IAAA,Y;EAGvB,C;;qBAAEC,CAAAA,EAAA;A;IAAA,a;EACmB,C;;qBAAGC,CAAeC,GAAfD,EAElF;A;IAAU,QAAI,gBAAJ,C,CACV;A,UAAUE,kBACX,IAAI,MAAJ,GAAa,GAAb,CAAiB,KAAjB,GAAyB,G;MAAY,OAAQ,KAAI,IACvD,OAAM,eAAN,EAAuB,CAAvB,CADuD,GAC3B,cADuB,C;IACH,C;EAAE,C;eAAGC,CAAAA,EAAW;A;QAChEC,UAGE,QACL,gBAAe,SAAf,C;IAAuC,IAAT,OAAS,S;MAAA,I;;MAAT,OAAS,S;;QACvCC,YAAgB,QAEjB,eAAc,KAAd,C;QAAkC,qBAAT,QAAS,K;IAAM,+B;MAAA,I;;MAAf,kBAAe,aACvC,SADuC,C;QACvBC,iB;QAAmBC,QAExB,W;QACVC,QAAY,W;QAAYC,SACnB,sBACP,IADO,EACD,OAAO,UAAP,GAA+B,MAA/B,CAAsC,WADrC,EACkD,GADlD,EACuD,MADvD,C;IACyF,MAC1F,CADiG,QACjG,KAAI,GAAJ,EAAQ,IAAR,EAAa,IAAb,C;QACaC,QAAY,W;IAE7B,KAAM,cACD,UAAM,OAAN,C;QAIDC,WAAe,mB;IAAoB,MAEzC,KAAI,QAAJ,C;IAAkB,KAAM,KAAI,MAAJ,C;QACHC,UACZ,iBAAa,QAAb,EAEF,GAFE,C;IAGT,KAAM,KAAI,OAAJ,C;QAAwCC,mBACzB,qBAET,QAFS,EAEC,GAFD,C;IAGZ,gBAA0B,CAAT,QAAS,KAAI,CAAC,GAAL,EAAS,GAAT,EAAa,GAAb,CAAiB,Y;;ICR8R,gBDS/T,cAClB,I;;ICVoY,gBDU3X,CACgB,MADhB,CACuB,OADvB,SACuC,I;;ICXiZ,gBDY5b,CAA6B,MAA7B,CAAoC,OAApC,UAAqD,I;IACrD,KAAM,KAAI,gBAAJ,C;QAA0CC,YAC/C,qBAAiB,QAAjB,EACN,GADM,C;IAGP,SAAmB,CAAT,QAAS,KAAI,GAAJ,EAAQ,GAAR,EAAc,CAAC,GAAf,CAAmB,Y;IAAgB,KAAM,KACzD,SADyD,C;QACpBC,WACxC,qBAAiB,QAAjB,EACC,GADD,C;IAEgB,QACJ,CAAT,QAAS,KAAI,GAAJ,EAAQ,GAAR,EAAY,GAAZ,C;IAAoB,KAAM,KAAI,QAAJ,C;QAChBC,WAAe,kBACzB,KACX,iBACa,IADb,GAEC,YAAW,IAAX,CAFD,EAE0B,sBACnB,kBADmB,CAF1B,CADW,CADyB,C;IAM7B,QAAS,eACZ,MADY,CACL,gBADK,C;IACiB,QAAS,SACzC,MADyC,CAE1C,UAF0C,EAE9B,MAF8B,CAEvB,WAFuB,C;;IC9BslC,QDiCxnC,CAAqB,SAArB,WAAyC,I;;ICjCioC,QDiCxnC,CAE1C,SAF0C,QAGlD,C;;ICpC0uC,QDoChtC,eAChB,C;;ICrCkyC,QDqCnwC,uBACtB,G;;ICtC40C,QDsCp0C,oBAC3B,M;IAAY,SAAU,aAAY,QAAZ,CAC/B,UAD+B,C;QACEC,SAAa,aAC7C,mBAD6C,EAEhC,QAFgC,C;QAEJC,YAElC,6B;;;;QACkBC,WEHmN,mC;;;;QFMvNC,cENwR,uC;QFOvQC,YAAgB,sC;QACpBC,cAAkB,KAAK,iBACvD,QAAO,CAAP,GAAW,EAD4C,EAAL,C;QAC9BC,SAAa,YAAM,wBAAN,C;QAC7BC,SAAa,YAAM,eAAN,C;cAEQ,sG;IAFc,MAAO,mBAE1C,SAF0C,EAE/B,QAF+B,OAwB5B,aAxB4B,C;gBA0BjC,M;IAAO,uBAEhB,QAFgB,EAEN,6BAFM,C;IAOkD,QACnE,kBAAiB,0DAAjB,C;EAOM,C;eAAIC,CAAAA,EAAW;A;QAClB,qBAAT,QAAS,eACR,cADQ,C;IACS,+B;MAAA,I;;MADlB,kBACkB,kBAAiB,OAAjB,EAA0B,WAA1B,C;;QAGhB,qBAD8B,QAC9B,K;IACD,+B;MAAA,I;;MAF+B,kBAE/B,kBAGU,UAHV,EAIQ,aAJR,C;;EAI6D,C;oCApCQC,CAtEhEC,S,EAsDiBC,O,EADYC,Y,EA5DhBC,M,EAPFC,K,EAAmBC,MAoFkCN,E;oBAAAO,CACtEC,MADsED,EAAA;A,UAE7CE,QAA4B,CAAhB,UAAM,SAAN,CAAgB,YAAU,MAAV,C;MAEpC,OAAO,KAAI,KAAJ,EAAW,YAAX,C;MAEhB,MAAM,KAAI,KAAJ,CAAI,EAAJ,C;MAA4B,YAAQ,I;MAE/C,oB;IAAA,C;G;sCAAGT,CAAAU,EAAAV,EAAA;A;IAAE,WAAW,EAAX,C;IAAe,oB;EAAA,C;6BAbuBW,CAXvBT,O,EAjEGU,O,EA+DaC,U,EApDjCZ,S,EAqD6BE,Y,EA5DhBC,M,EAPFC,K,EAAmBC,MA+EUK,E;oBAAAX,CAC7Cc,eAD6Cd,EAAA;A,MACb,OAC5B,KAAI,OAAJ,EACH,KAAK,iBAEE,eAFF,EAAL,CADG,C;gBAGmC,iB;kBAA8B,kF;MAAhB,SAAK,UAAL,SAQhD,2BARgD,C;MAQpB,oB;IAAA,C;G;+BAAGW,CAAED,EAAFC,EAAA;A;IAAQ,WACtC,EADsC,C;IAClC,oB;EAAA,C;sBAtBiBI,CAvEXV,K,EAoEMH,O,EAChBc,O,EAJeC,Y,EA9DIL,O,EA+DaC,U,EApDjCZ,S,EAqD6BE,Y,EA5DhBC,M,EAPiBE,MAuERS,E;oBAAAJ,CAAEO,GAAFP,EAAA;A,MAC3B,WACK,GADL,CACS,I;MAAc,OAAO,KAEpB,KAFoB,CAEpB,EAFoB,EAEd,KAAsB,iBAAe,GAAf,CAAmB,SAAnB,GACd,cACb,IADa,CADc,CAAtB,CAFc,C;gBAMe,gG;MAcjC,OAdT,OAAO,eAAc,YAAd,EAA2B,OAA3B,OAa0B,oBAb1B,C;IAcO,C;G;wBAAGI,CAAEL,EAAFK,EAAA;A;IAAQ,WAEzB,EAFyB,C;IAErB,oB;EAAA,C;wBAESA,CA9FSH,O,EAsDOO,OAwChBJ,E;oBAAAJ,CAAE,mBAAFA,EAAA;A,MAAe,OAC7B,UAAS,OAAO,UAAP,GACP,MADO,CACA,W;MAAqB,OAE7B,yB;MAAkC,OAAO,SACtC,MADsC,CAC/B,UAD+B,EACnB,MADmB,CACZ,WADY,C;MACM,oB;IAAA,C;G;wBAC5BI,CAvGaT,M,EAoEbJ,O,EAjErBkB,M,EAsD+BD,O,EAlDdf,M,EAJOQ,OAoGHG,E;oBAAAJ,CAAAA,EAAA;A;MAAc,c;QAEtB,OAAO,QAAO,MAAM,WAAb,C;;;MACpB,OACD,QAEG,MAFH,EAEU,OAFV,C;MAGS,oB;IAAA,C;G;sBAEkCU,CAAAX,EAAAW,EAAA;A;cACpC,M;IACJ,SAAK,kBAAL,C;IAAoB,oB;EAAA,C;6BAAfC,CAAAZ,EAAAY,EAAA;A;IAAE,M;IAAO,oB;EAAA,C;wBAMTD,CAAAX,EAAAW,EAAA;A;QAAmB,qBAAT,QAAS,K;IAAM,+B;MAAA,I;;MAAf,kBAAe,oB;IAAwB,oB;EAAA,C;;2CAtJ1DE,CAAAA,E;;;sBAUmB,QAAQ,gCAAR,C,CAA0C,a;kBAC/C,QAAQ,4BAAR,C,CAAsC,S;2BAC9B,QAAQ,uCAAR,C,CAAiD,kB;uBAG1C,CAAYC,KAAZ,E;QAAmB,OAAO,IAAI,KAAJ,E;O;wBACZ,CAAYA,K,EAAMC,GAAlB,E;QAAuB,OAAO,IAAI,KAAJ,CAAU,GAAV,C;O;;G;;;;;;;"}