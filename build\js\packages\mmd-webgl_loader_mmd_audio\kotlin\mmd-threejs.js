(function (factory) {
  if (typeof define === 'function' && define.amd)
    define(['exports'], factory);
  else if (typeof exports === 'object')
    factory(module.exports);
  else
    globalThis['mmd-threejs'] = factory(typeof globalThis['mmd-threejs'] === 'undefined' ? {} : globalThis['mmd-threejs']);
}(function (_) {
  'use strict';
  //region block: pre-declaration
  //endregion
  return _;
}));

//# sourceMappingURL=mmd-threejs.js.map
