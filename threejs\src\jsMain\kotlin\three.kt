@file:JsModule("three")
@file:JsNonModule

package three

import org.w3c.dom.Element
import kotlin.js.Json

// Core classes
external class Scene {
    fun add(obj: dynamic)
    var background: Color?
}

external class Camera {
    var aspect: Double
    fun updateProjectionMatrix()
    fun add(obj: dynamic)
}

external class PerspectiveCamera(fov: Double, aspect: Double, near: Double, far: Double) {
    var aspect: Double
    val position: Vector3
    fun updateProjectionMatrix()
    fun add(obj: dynamic)
}

external class WebGLRenderer(parameters: Json? = definedExternally) {
    val domElement: Element
    fun setPixelRatio(value: Double)
    fun setSize(width: Int, height: Int)
    fun setAnimationLoop(callback: () -> Unit)
}

// Objects
external class Object3D {
    val position: Vector3
    fun add(obj: dynamic)
}

external class Mesh(geometry: BufferGeometry? = definedExternally, material: Material? = definedExternally) {
    val position: Vector3
    fun add(obj: dynamic)
}

external class SkinnedMesh(geometry: BufferGeometry? = definedExternally, material: Material? = definedExternally) {
    val position: Vector3
    fun add(obj: dynamic)
}

// Geometry
external class BufferGeometry

// Materials
external class Material

external class MeshBasicMaterial(parameters: Json? = definedExternally)

// Math
external class Vector3(x: Float = definedExternally, y: Float = definedExternally, z: Float = definedExternally) {
    fun set(x: Float, y: Float, z: Float): Vector3
    fun normalize(): Vector3
}

external class Color(color: Int) {
    constructor(r: Float, g: Float, b: Float)
}

external class Clock {
    fun getDelta(): Double
}

// Lights
external class Light {
    val position: Vector3
    fun add(obj: dynamic)
}

external class AmbientLight(color: Int, intensity: Float) {
    val position: Vector3
    fun add(obj: dynamic)
}

external class DirectionalLight(color: Int, intensity: Float) {
    val position: Vector3
    fun add(obj: dynamic)
}

// Helpers
external class PolarGridHelper(radius: Double, sectors: Double) {
    val position: Vector3
    fun add(obj: dynamic)
}

// Audio
external class AudioListener {
    val position: Vector3
    fun add(obj: dynamic)
}

external class Audio(listener: AudioListener) {
    fun setBuffer(buffer: dynamic): Audio
}

external class AudioLoader {
    fun load(url: String, onLoad: (buffer: dynamic) -> Unit, onProgress: ((xhr: dynamic) -> Unit)? = definedExternally)
}
