import kotlinx.browser.document
import kotlinx.browser.window
import three.*
import kotlin.js.Promise
import kotlin.js.json

external val Math: dynamic
external fun require(module: String): dynamic
external val Ammo: () -> Promise<Unit>

val OutlineEffect: dynamic = require("./jsm/effects/OutlineEffect.js").OutlineEffect
val MMDLoader: dynamic = require("./jsm/loaders/MMDLoader.js").MMDLoader
val MMDAnimationHelper: dynamic = require("./jsm/animation/MMDAnimationHelper.js").MMDAnimationHelper


val jsNew: (classDef: dynamic) -> dynamic = js("function(Clazz){return new Clazz()}")
val jsNew1: (classDef: dynamic, arg: dynamic) -> dynamic = js("function(Clazz,arg){return new Clazz(arg)}")

fun onProgress(xhr: dynamic) {
    if (xhr.lengthComputable) {
        val percentComplete = xhr.loaded / xhr.total * 100
        console.log(Math.round(percentComplete, 2) + "% downloaded")
    }
}

fun init() {
    val overlay = document.getElementById("overlay")
    overlay?.remove();

    val container = document.createElement("div")
    document.body?.appendChild(container)

    var mesh: Mesh
    var ready: Boolean = false
    val clock = Clock()
    val camera = PerspectiveCamera(45.0, window.innerWidth.toDouble() / window.innerHeight, 1.0, 2000.0)

    // Scene
    val scene = Scene()
    scene.background = Color(0x87ceeb) // 天空蓝背景，更适合卡通风格

    // 移除网格，让场景更简洁
    // scene.add(PolarGridHelper(30.0, 0.0))

    val listener = AudioListener()
    camera.add(listener)
    scene.add(camera)

    // 增强环境光，让模型更明亮
    val ambient = AmbientLight(0xffffff, 0.8f)
    scene.add(ambient)

    // 主光源 - 更柔和的光照
    val directionalLight = DirectionalLight(0xffffff, 1.2f)
    directionalLight.position.set(-1f, 1f, 1f).normalize()
    scene.add(directionalLight)

    // 添加补光，减少阴影
    val fillLight = DirectionalLight(0xffffff, 0.6f)
    fillLight.position.set(1f, 0.5f, -1f).normalize()
    scene.add(fillLight)

    // 添加顶光，增强立体感
    val topLight = DirectionalLight(0xffffff, 0.4f)
    topLight.position.set(0f, 1f, 0f)
    scene.add(topLight)

    // renderer - 增强渲染设置
    val renderer = WebGLRenderer(json(
        "antialias" to true,
        "alpha" to true,
        "powerPreference" to "high-performance"
    ))
    renderer.setPixelRatio(window.devicePixelRatio)
    renderer.setSize(window.innerWidth, window.innerHeight)

    // 启用阴影和色调映射，增强视觉效果
    renderer.asDynamic().shadowMap.enabled = true
    renderer.asDynamic().shadowMap.type = 2 // PCFSoftShadowMap
    renderer.asDynamic().toneMapping = 1 // ACESFilmicToneMapping
    renderer.asDynamic().toneMappingExposure = 1.2
    renderer.asDynamic().outputColorSpace = "srgb"

    container.appendChild(renderer.domElement)

    // 增强轮廓效果设置
    val effect = jsNew1(OutlineEffect, renderer)

    // model
    val modelFile = "models/mmd/miku/miku_v2.pmd";
    val vmdFiles = arrayOf("models/mmd/vmds/wavefile_v2.vmd")
    val cameraFiles = arrayOf("models/mmd/vmds/wavefile_camera.vmd")
    val audioFile = "models/mmd/audios/wavefile_short.mp3"
    val audioParams = json("delayTime" to 160f * 1 / 30)

    val helper = jsNew(MMDAnimationHelper)

    val loader = jsNew(MMDLoader)

    loader.loadWithAnimation(modelFile, vmdFiles, { mmd ->
        mesh = mmd.mesh;
        helper.add(
            mesh, json(
                "animation" to mmd.animation,
                "physics" to true
            )
        )

        loader.loadAnimation(cameraFiles, camera, { cameraAnimation ->

            helper.add(camera, json("animation" to cameraAnimation))

            AudioLoader().load(audioFile, { buffer ->

                val audio = Audio(listener).setBuffer(buffer);

                helper.add(audio, audioParams);
                scene.add(mesh);

                ready = true;

            }, { onProgress(it) })
        }, { it -> onProgress(it) })
    }, { it -> onProgress(it) })

    window.addEventListener("resize", { _ ->
        camera.aspect = window.innerWidth.toDouble() / window.innerHeight;
        camera.updateProjectionMatrix()

        effect.setSize(window.innerWidth, window.innerHeight);
    })


    // drive
    renderer.setAnimationLoop {
        if (ready) {
            helper.update(clock.getDelta());
        }
        effect.render(scene, camera);
    }
}


fun main() {
    document.querySelector("#startButton")?.addEventListener("click", {
        Ammo().then { init() }
    })

    document.body?.addEventListener("dblclick", {
        document.body?.requestFullscreen()
    });
}
