{"version": 3, "sources": ["js/runtime/globalThis.kt", "src/js-builtin-sources/core/builtins/src/kotlin/Unit.kt", "js/runtime/BitMask.kt", "js/runtime/coreRuntime.kt", "js/src/kotlin/dynamic.kt", "js/runtime/hacks.kt", "js/runtime/metadataUtils.kt", "js/runtime/typeCheckUtils.kt", "src/kotlin/util/Standard.kt", "js/runtime/void.kt", "js/src/kotlin/exceptions.kt", "js/src/kotlin/json.kt", "src/kotlin/util/Tuples.kt"], "sourcesContent": [null, null, null, null, null, null, null, null, null, null, null, null, null], "names": ["<init>", "implement", "interfaces", "maxSize", "masks", "i", "currentSize", "imask", "iid", "iidImask", "bitMaskWith", "activeBit", "numberIndex", "intArray", "positionInNumber", "numberWithSettledBit", "compositeBitMask", "capacity", "result", "mask", "objectCreate", "proto", "defineProp", "obj", "name", "getter", "setter", "equals", "obj1", "obj2", "captureStack", "instance", "constructorFunction", "protoOf", "constructor", "extendThrowable", "this_", "message", "cause", "setPropertiesToThrowableInstance", "errorInfo", "THROW_CCE", "createMetadata", "kind", "defaultConstructor", "associatedObjectKey", "associatedObjects", "suspendArity", "undef", "generateInterfaceId", "initMetadataFor", "ctor", "parent", "metadata", "receiver", "initMetadataForClass", "initMetadataForObject", "initMetadataForLambda", "initMetadataForCoroutine", "initMetadataForFunctionReference", "initMetadataForCompanion", "calculateErrorInfo", "parentProto", "hasProp", "propName", "getPrototypeOf", "<get-VOID>", "<init properties void.kt>", "Exception_init_$Init$", "Exception_init_$Create$", "RuntimeException_init_$Init$", "RuntimeException_init_$Create$", "ClassCastException_init_$Init$", "ClassCastException_init_$Create$", "json", "pairs", "res", "value", "first", "second", "component1", "component2", "other", "to", "<this>", "that"], "mappings": ";AAkBC,UAVQ,CAAA,E;EACL,IAAI,OAAO,UAAW,KAAI,QAA1B,C;IAAoC,M;EACpC,MAAqB,CAAd,cAAc,CAAC,MAAD,CAAQ,SAAR,EAAmB,WAAnB,EAAgC,CACjD,GADiD,WACpC,CAAA,E;IACT,OAAO,I;GAFsC,EAIjD,YAJiD,EAInC,IAJmC,CAAhC,C;EAMrB,SAAqB,CAAX,UAAW,GAAE,S;EACvB,OAAO,MAAP,CAAc,SAAd,CAAwB,S;CAC3B,G;;;;;;;;;;;;;;;;;;eCGDA,CAAAA,EAAA;A,EAAA,C;;;;;oBCiBSC,CAAcC,UAAdD,EAAmD;A,QACxDE,UAAc,C;QACdC,QAAY,E;QAEF,qB;QAAA,wB;WAAV,oBAAU,I,EAAV;A,UAAKC,IAAK,6B;MAAA,6C;UACNC,cAAkB,O;UACI,mBAAE,SAAF,CAAY,O;UAAlCC,QAAsB,yBAAyB,CAAzB,CAA2B,OAA3B,iB;MAEtB,IAAI,WAAS,IAAT,CAAJ,C,CAAmB;A,QACf,KAAM,MAAK,KAAL,C;QACN,cAAc,KAAd,CAAoB,M;MACxB,C;UAEAC,MAAgB,CAAhBA,CAAkB,UAAlBA,CAA+B,G;;MACD,IAAL,GAAK,S;cAAA,I;;;;;cAAM,YAAX,GAAW,C;;UAApCC,c;MAEA,IAAI,cAAY,IAAZ,CAAJ,C,CAAsB;A,QAClB,KAAM,MAAK,QAAL,C;QACN,cAAc,IAAO,KAAI,WAAJ,EAAiB,QAAjB,CAA0B,MAA1B,C;MACzB,C;MAEA,IAAI,cAAc,OAAlB,C,CAA2B;A,QACvB,UAAU,W;MACd,C;;IAGJ,OAAO,iBAAiB,OAAjB,EAA0B,KAA1B,C;EACX,C;sBAxDQC,CAAgBC,SAAhBD,EAAyC;A,QAC7CE,cAAkB,aAAc,C;QAChCC,WAAe,eAAS,cAAc,CAAvB,K;QACfC,mBAAuB,YAAc,E;QACrCC,uBAA2B,KAAM,gB;IACjC,SAAS,WAAT,IAAwB,SAAS,WAAT,IAAyB,oB;IACjD,OAAO,Q;EACX,C;2BAUQC,CAAqBC,Q,EAAeb,KAApCY,EAAoE;A,QACjE,O;QAAA,uBAAS,QAAT,C;WAAA,MAAS,Q,EAAT;A,UAAA,W;UACHE,SAAa,C;UACA,qB;UAAA,mB;aAAb,oBAAa,I,EAAb;A,YAAKC,OAAQ,wB;QAAA,6C;QACT,IAAI,QAAI,IAAJ,CAAS,MAAb,C,CAAmB;A,UACf,SAAS,SAAU,KAAK,KAAL,C;QACvB,C;;MALD,eAOH,M;MAPG,iB;IAQP,C;IARA,OAAO,K;EASX,C;uBC8KSC,CAAqBC,KAArBD,EACL;A,6BADsC,I;IACZ,OAAvB,MAAH,CAAU,MAAV,O;EAAyB,C;qBAkCpBE,CAAeC,G,EAAUC,I,EAAcC,M,EAAcC,MAArDJ,EACL;A,IAAwF,OAArF,MAAqB,CAAd,cAAc,YAAY,CAAE,YAAF,EAAgB,IAAhB,EAAsB,GAAtB,UAAmC,GAAnC,SAAZ,C;EAA+D,C;iBA/OlFK,CAAWC,I,EAAeC,IAA1BF,EAAkD;A,IACvD,IAAI,QAAQ,IAAZ,C,CAAkB;A,MACd,OAAO,QAAQ,I;IACnB,C;IACA,IAAI,QAAQ,IAAZ,C,CAAkB;A,MACd,OAAO,K;IACX,C;IAEA,IAAI,OAAS,IAAT,KAAkB,QAAtB,IAAkC,OAAS,IAAT,CAAc,MAAd,KAAyB,UAA3D,C,CAAuE;A,MACnE,OAAQ,IAAD,CAAM,MAAN,CAAc,IAAd,C;IACX,C;IAEA,IAAI,SAAS,IAAb,C,CAAmB;A,MACf,OAAO,SAAS,I;IACpB,C;IAEA,IAAI,OAAS,IAAT,KAAkB,QAAtB,IAAkC,OAAS,IAAT,KAAkB,QAApD,C,CAA8D;A;MACnD,aAAS,IAAT,C;;QAAkB,aAAS,CAAT,C;kBAAA,I;;;sBCJuB,CDIT,GAAgB,I;;kBAAhB,UCJkC,CDIT,GAAgB,I;;;;cAAvD,K;;MAAzB,U;IACJ,C;IACA,OAAO,SAAS,I;EACpB,C;uBAoHSG,CAAiBC,Q,EAAqBC,mBAAtCF,EAAgE;A,IACrE,IAAI,MAAY,iBAAZ,IAAiC,IAArC,C,CAA2C;A,MACvC,KAAY,mBAAkB,QAAlB,EAA4B,mBAA5B,C;IAChB,C,MAAO;A;MCnGygG,QDoG5gG,UAA6B,IAAO,KAAP,EAA7B,EAA+C,K;IACnD,C;EACJ,C;kBAyDSG,CAAYC,WAAZD,EACL;A,IAA2B,mBAAZ,S;EAAW,C;0BA9CrBE,CAAoBC,K,EAAgBC,O,EAAkBC,KAAtDH,EAAyE;A,IAC9E,KAAY,MAAK,KAAL,C;IACZ,iCAAiC,KAAjC,EAAwC,OAAxC,EAAiD,KAAjD,C;EACJ,C;2CAESI,CAAqCH,K,EAAgBC,O,EAAkBC,KAAvEC,EAA0F;A,QAC/FC,YAAgB,mBAAmB,MAAS,gBAAe,KAAf,CAA5B,C;IAChB,IAAI,CAAC,YAAc,CAAf,MAAuB,CAA3B,C,CAA8B;A;MAEV,IAAI,WAAW,IAAf,C;;QAEZ,IAAI,cAAY,IAAZ,CAAJ,C;cAEI,iBAAA,KAAO,kBAAP,KAAO,W;kBAAP,yBAAqB,IAArB,iB;;kBAGA,I;;;;cAED,O;;MATP,mB;IAUJ,C;IACA,IAAI,CAAC,YAAc,CAAf,MAAuB,CAA3B,C,CAA8B;A,MAC1B,cAAc,K;IAClB,C;IACA,aAAa,MAAS,gBAAe,KAAf,CAAtB,CAA4C,WAA5C,CAAwD,I;EAC5D,C;oBEpKSC,CAAAA,EAAyB;A,IAC9B,MAAM,kC;EACV,C;yBCwFSC,CACLC,I,EACAnB,I,EACAoB,kB,EACAC,mB,EACAC,iB,EACAC,YANKL,EAOG;A,QACRM,QAAY,I;QACZxC,MAAc,SAAQ,WAAZ,GAAqC,qBAArC,GAAgE,I;IAC1E,OAAO,CACP,IADO,QAEP,UAFO,QAGP,mBAHO,uBAIP,iBAJO,qBAKP,YALO,gBAMP,QANO,SAOP,kBAPO,sBAQP,GARO,M;EAUX,C;8BAvHQyC,CAAAA,EAA+B;A,IACnC,IAAI,sBAAsB,IAA1B,C,CAAgC;A,MAC5B,oBAAoB,C;IACxB,C;;IACA,oBFQD,iBERqB,GAAsC,CAA1D,I;;IACA,OFQsB,iB;EEP1B,C;;0BAMSC,CACLP,I,EACAQ,I,EACA3B,I,EACAoB,kB,EACAQ,M,EACAlD,U,EACA6C,Y,EACAF,mB,EACAC,iBATKI,EAUP;A,IACE,IAAI,YAAU,IAAV,CAAJ,C,CAAoB;A,MAEC,KAAV,SAAU,GAAE,MAAa,CAAN,MAAM,QAAQ,SAAR,C;MACH,KAAtB,SAAsB,CAAZ,WAAY,O;IAEjC,C;QAEAG,WAAe,eAAe,IAAf,EAAqB,IAArB,EAA2B,kBAA3B,EAA+C,mBAA/C,EAAoE,iBAApE,EAAuF,YAAvF,C;IACf,kBAAoB,Q;IAEpB,IAAI,gBAAc,IAAd,CAAJ,C,CAAwB;A,UACpBC,WAAmB,iBAAS,GAAT,EAAgB,IAAhB,CAAJ,GAA0B,IAA1B,GAAoC,IAApC,CAAyC,S;MACxD,mBAAqB,UAAU,UAAV,C;IACzB,C;EACJ,C;+BAESC,CACLJ,I,EACA3B,I,EACAoB,kB,EACAQ,M,EACAlD,U,EACA6C,Y,EACAF,mB,EACAC,iBARKS,EASP;A,QACEZ,OAAW,O;IACX,gBAAgB,IAAhB,EAAsB,IAAtB,EAA4B,IAA5B,EAAkC,kBAAlC,EAAsD,MAAtD,EAA8D,UAA9D,EAA0E,YAA1E,EAAwF,mBAAxF,EAA6G,iBAA7G,C;EACJ,C;gCAESa,CACLL,I,EACA3B,I,EACAoB,kB,EACAQ,M,EACAlD,U,EACA6C,Y,EACAF,mB,EACAC,iBARKU,EASP;A,QACEb,OAAW,Q;IACX,gBAAgB,IAAhB,EAAsB,IAAtB,EAA4B,IAA5B,EAAkC,kBAAlC,EAAsD,MAAtD,EAA8D,UAA9D,EAA0E,YAA1E,EAAwF,mBAAxF,EAA6G,iBAA7G,C;EACJ,C;gCAgBSc,CAA0BN,I,EAAYC,M,EAAelD,U,EAA6B6C,YAAlFU,EAA6G;A,IAClH,qBAAqB,IAArB,EAA2B,QAA3B,EAAqC,IAArC,EAA2C,MAA3C,EAAmD,UAAnD,EAA+D,YAA/D,EAA6E,IAA7E,EAAmF,IAAnF,C;EACJ,C;mCAMSC,CAA6BP,I,EAAYC,M,EAAelD,U,EAA6B6C,YAArFW,EAAgH;A,IACrH,qBAAqB,IAArB,EAA2B,WAA3B,EAAwC,IAAxC,EAA8C,MAA9C,EAAsD,UAAtD,EAAkE,YAAlE,EAAgF,IAAhF,EAAsF,IAAtF,C;EACJ,C;2CANSC,CAAqCR,I,EAAYC,M,EAAelD,U,EAA6B6C,YAA7FY,EAAwH;A,IAC7H,qBAAqB,IAArB,EAA2B,mBAA3B,EAAgD,IAAhD,EAAsD,MAAtD,EAA8D,UAA9D,EAA0E,YAA1E,EAAwF,IAAxF,EAA8F,IAA9F,C;EACJ,C;mCAMSC,CAA6BT,I,EAAYC,M,EAAelD,U,EAA6B6C,YAArFa,EAAgH;A,IACrH,sBAAsB,IAAtB,EAA4B,WAA5B,EAAyC,IAAzC,EAA+C,MAA/C,EAAuD,UAAvD,EAAmE,YAAnE,EAAiF,IAAjF,EAAuF,IAAvF,C;EACJ,C;6BC3FSC,CAAuBxC,KAAvBwC,EAA4C;A,QACjB,qBAAN,KAAM,Y;QAAhCR,WAAgC,kBAAa,kBAAb,kBAAa,W;QAEnC,qBAAV,QAAU,kBAAV,QAAU,U;IAAW,IAAX,kBAAW,S;MAAA,I;SAAA;A;;MCCd,OAHsE,kB;;QDI7EnC,SAAa,C;IACb,IAAI,QAAQ,KAAR,EAAe,SAAf,CAAJ,C;MAA+B,SAAS,SAAU,C;IAClD,IAAI,QAAQ,KAAR,EAAe,OAAf,CAAJ,C;MAA6B,SAAS,SAAU,C;IAEhD,IAAI,aAAU,CAAV,CAAJ,C,CAAmB;A,UACf4C,cAAkB,eAAe,KAAf,C;MAClB,IAAI,eAAe,KAAf,CAA2B,SAA/B,C,CAA0C;A,QACtC,SAAS,SAAU,mBAAmB,WAAnB,C;MACvB,C;IACJ,C;IAEA,IAAI,cAAY,IAAZ,CAAJ,C,CAAsB;A,MAClB,qBAAqB,M;IACzB,C;IAEA,OAAO,M;EACX,C;kBAvBQC,CAAY1C,K,EAAgB2C,QAA5BD,EAAyD;A,IAA8B,OAA9B,KAAM,gBAAe,QAAf,C;EAAuB,C;yBAyBtFE,CAAmB1C,GAAnB0C,EAAmC;A,IAA4B,OAA5B,MAAS,gBAAe,GAAf,C;EAAkB,C;mBEhC7DC,CAAAA,EAAA;A;IAAA,W;EAAgC,C;;;2CAPzCC,CAAAA,E;;;aAO8B,KAAQ,C;;G;gCCQpBC,C,KAAAA,E;IAAgB,sB;IAA9B,qB;;G;kCAAcC,CAAAA,E;;;;G;uBADlB;A;EAAA,C;uCAQkBC,C,KAAAA,E;IAAgB,4B;IAA9B,4B;;G;yCAAcC,CAAAA,E;;;;G;8BADlB;A;EAAA,C;yCAqDkBC,C,KAAAA,E;IAAgB,mC;IAA9B,8B;;G;2CAAcC,CAAAA,E;;;;G;gCADlB;A;EAAA,C;eCnBOC,CAASC,KAATD,EAAiD;A,QACpDE,MAAmB,E;QACG,qB;QAAA,mB;WAAtB,oBAAsB,I,EAAtB;A,UAAsB,8C;MAAA,6C;UAAhBpD,OAAgB,mBAAhB,I;UAAMqD,QAAU,mBAAV,I;MACR,IAAI,IAAJ,IAAY,K;;IAEhB,OAAO,G;EACX,C;eCnCoC7E,CAChC8E,K,EACAC,MAFgC/E,EAAA;A,IAChC,gB;IACA,iB;EAFgC,C;6BAzBpCgF,CAAAA,EAAA;A,IAAA,e;EAAA,C;6BAAAC,CAAAA,EAAA;A,IAAA,e;EAAA,C;kCAAAtD,CAAAuD,KAAAvD,EAAA;A,IAAA,mB;MAAA,W;IAAA,6B;MAAA,Y;QAAA,kE;IAAA,gD;MAAA,Y;IAAA,gD;MAAA,Y;IAAA,W;EAAA,C;aA0CawD,CAAWC,a,EAAKC,IAAhBF,EAAuC;A,IAAgB,OAAhB,SAAK,aAAL,EAAW,IAAX,C;EAAe,C;;;;;;;;;;;;"}