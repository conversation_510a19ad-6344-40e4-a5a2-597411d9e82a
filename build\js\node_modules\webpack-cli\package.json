{"name": "webpack-cli", "version": "5.1.4", "description": "CLI for webpack & friends", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/webpack/webpack-cli.git"}, "homepage": "https://github.com/webpack/webpack-cli/tree/master/packages/webpack-cli", "bugs": "https://github.com/webpack/webpack-cli/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "bin": {"webpack-cli": "./bin/cli.js"}, "main": "./lib/index.js", "engines": {"node": ">=14.15.0"}, "keywords": ["webpack", "cli", "scaffolding", "module", "bundler", "web"], "files": ["bin", "lib", "!**/*__tests__"], "dependencies": {"@discoveryjs/json-ext": "^0.5.0", "@webpack-cli/configtest": "^2.1.1", "@webpack-cli/info": "^2.0.2", "@webpack-cli/serve": "^2.0.5", "colorette": "^2.0.14", "commander": "^10.0.1", "cross-spawn": "^7.0.3", "envinfo": "^7.7.3", "fastest-levenshtein": "^1.0.12", "import-local": "^3.0.2", "interpret": "^3.1.1", "rechoir": "^0.8.0", "webpack-merge": "^5.7.3"}, "devDependencies": {"@types/envinfo": "^7.8.1"}, "peerDependencies": {"webpack": "5.x.x"}, "peerDependenciesMeta": {"@webpack-cli/generators": {"optional": true}, "webpack-bundle-analyzer": {"optional": true}, "webpack-dev-server": {"optional": true}}, "gitHead": "e07f0e58df103011435524d757102534b75a6796"}